// src/router/publicRoutes.js
import HomeView from '@/views/public/base/Home.vue'
import component from 'element-plus/es/components/tree-select/src/tree-select-option.mjs'

const routes = [
  {
    path: '/',
    component: () => import('@/views/DefaultLayout.vue'), // 默认布局（带常规导航栏）
    children: [
      {
        path: '', //首页
        name: 'Home',
        component: HomeView
      },
      { //专业内容
        path: 'pro-content',
        name: 'pro-content',
        component: () => import('@/views/public/profession/Pro_Content.vue'),
      },
      {
        path: 'course-center',
        name: 'course-center',
        component: () => import('@/views/public/course/index.vue'),
      },
      {
        path: '/course-center/:courseId',
        name: 'course-detail',
        component: () => import('@/views/public/course/Course_Center/index.vue'),
        props: true,
        meta: { requiresAuth: true },
        redirect: { name: 'course-overview' }, // 添加重定向
        children: [
          {
            path: 'overview',
            name: 'course-overview',
            component: () => import('@/views/public/course/Course_Center/Overview/index.vue'),
            props: true
          },
          {
            path: 'design',
            name: 'course-design',
            component: () => import('@/views/public/course/Course_Center/Design/index.vue')
          },
          {
            path: 'knowledge-graph',
            name: 'course-knowledge-graph',
            component: () => import('@/views/public/course/Course_Center/KnowledgeGraph/index.vue'),
            props: true
          },
          {
            path: 'question-graph',
            name: 'course-question-graph',
            component: () => import('@/views/public/course/Course_Center/QuestionGraph/index.vue'),
            props: true
          },
          {
            path: 'learning-path',
            name: 'course-learning-path',
            component: () => import('@/views/public/course/Course_Center/LearningPath/index.vue'),
            props: true
          },
          {
            path: 'resources',
            name: 'course-resources',
            component: () => import('@/views/public/course/Course_Center/Resources/index.vue')
          },
          {
            path: 'ability-graph',
            name: 'course-ability-graph',
            component: () => import('@/views/public/course/Course_Center/AbilityGraph/index.vue'),
            props: true
          },
          {
            path: 'team',
            name: 'teaching-team',
            component: () => import('@/views/public/course/Course_Center/Team/index.vue')
          },

          {
            path: 'team',
            name: 'teaching-team',
            component: () => import('@/views/public/course/Course_Center/Team/index.vue')
          },
          {
            path: 'discuss',
            name: 'course-discuss',
            component: () => import('@/views/public/course/Course_Center/Discuss/index.vue')
          },
          {
            path: 'chapter',
            name: 'course-chapter',
            component: () => import('@/views/public/course/Course_Center/Chapter/index.vue'),
          },
          {
            path: 'review',
            name: 'course-review',
            component: () => import('@/views/public/course/Course_Center/Review/index.vue'),
            props: true
          }
        ]
      },
      { //知识图谱
        path: 'course-map',
        name: 'course-map',
        component: () => import('@/views/public/course/Course_Map/index.vue')
      },
      { //数媒资源
        path: 'db-resource',
        name: 'db-resource',
        component: () => import('@/views/public/resource/index.vue')
        //component: () => import('@/views/public/resource/pages/VirtualProject.vue')

      },
      { //非遗详情页
        path: 'heritage-detail/:id',
        name: 'heritage-detail',
        component: () => import('@/views/public/resource/pages/HeritageDetailPage.vue'),
        props: true
      },
      { //虚拟仿真项目详情页
        path: 'virtual-project/:simulationId',
        name: 'virtual-project',
        component: () => import('@/views/public/resource/pages/VirtualProject.vue'),
        props: true
      },
      { //毕业设计作品详情页
        path: 'graduation-project/:workId',
        name: 'graduation-project',
        component: () => import('@/views/public/resource/pages/GraduationProjects.vue'),
        props: true
      },
      { //数媒智能广场
        path: 'intelligent',
        name: 'intelligent',
        component: () => import('@/views/public/intelligent/FunctionHome.vue'),
        // meta: { requiresAuth: true } ,// 需要登录
        children: [
          {
            path: '',
            name: 'photo',
            component: () => import('@/views/public/intelligent/Components/AIPhoto.vue')
          },
          {
            path: 'video',
            name: 'video',
            component: () => import('@/views/public/intelligent/Components/AIVideo.vue')
          },
          {
            path: 'dialog',
            name: 'dialog',
            component: () => import('@/views/public/intelligent/Components/AIDialog.vue')
          },
          {
            path: 'writer',
            name: 'writer',
            component: () => import('@/views/public/intelligent/Components/AIWriter.vue')
          },
          {
            path: 'dialog',
            name: 'dialog',
            component: () => import('@/views/public/intelligent/Components/AIDialog.vue')
          },
          {
            path: 'note',
            name: 'note',
            component: () => import('@/views/public/intelligent/Components/AINote.vue')
          },
          {
            path: 'selftest',
            name: 'selftest',
            component: () => import('@/views/public/intelligent/Components/AISelftest.vue')
          },
          {
            path: 'question',
            name: 'question',
            component: () => import('@/views/public/intelligent/Components/AIQuestion.vue')
          }

        ]
      },
      { //三大方向-虚拟仿真
        path: 'three-direction/simulation-xr',
        name: 'simulation-xr',
        component: () => import('@/views/public/threeDirection/simulationXR/index.vue')
      },
      { //三大方向-全栈开发
        path: 'three-direction/full-stack',
        name: 'full-stack',
        component: () => import('@/views/public/threeDirection/fullStack/index.vue')
      },
      { //三大方向-数字视觉
        path: 'three-direction/digital-visual',
        name: 'digital-visual',
        component: () => import('@/views/public/threeDirection/DVandAE/index.vue')
      },


      // 其他需要导航栏的路由...
    ]

  },
  {
    path: '/course_map_overview/:graphId',
    name: 'course_map_overview',
    component: () => import('@/views/public/course/Course_Map/Overview/index.vue'),
  },
  {
    path: '/login',
    component: () => import('@/views/BlankLayout.vue'), // 空白布局（无导航栏）
    children: [
      {
        path: '',
        name: 'Login',
        component: () => import('@/views/auth/Login.vue')
      }
    ]
  },
  {
    path: '/register',
    children: [
      {
        path: '',
        name: 'Register',
        component: () => import('@/views/auth/Register.vue')
      }
    ]
  },
  {
    path: '/tea-account',
    name: 'teacher-account',
    component: () => import('@/views/public/course/Course_Center/Team/index.vue'),
  },
  {
    path: '/ability-detail/:abilityName',
    name: 'ability-detail',
    component: () => import('@/views/public/course/Course_Center/AbilityGraph/AbilityDetail.vue'),
    props: true
  }
]
export default routes
