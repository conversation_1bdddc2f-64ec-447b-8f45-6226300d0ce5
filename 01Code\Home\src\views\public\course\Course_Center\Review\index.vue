<template>
  <div class="course-review-container">
    <div class="stats-header">
      <h2>课程数据统计</h2>
      <div class="course-info">
        <div class="course-name">{{ courseInfo.name || '加载中...' }}</div>
        <div class="course-meta">
          <span v-if="courseInfo.major">专业: {{ courseInfo.major }}</span>
          <span v-if="courseInfo.subjectCategory">学科: {{ courseInfo.subjectCategory }}</span>
        </div>
        <div class="course-id">课程ID: {{ courseId }}</div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载数据...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <h3>加载失败</h3>
      <p>{{ error }}</p>
      <button @click="fetchStats" class="retry-btn">重试</button>
    </div>
    
    <!-- 数据展示 -->
    <div v-else>
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-number">{{ stats.courseTotalPosts }}</div>
          <div class="stat-label">总帖子数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.courseTotalParticipants }}</div>
          <div class="stat-label">参与人数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.courseTotalPostLikes }}</div>
          <div class="stat-label">总点赞数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.courseTotalPostStars }}</div>
          <div class="stat-label">总收藏数</div>
        </div>
      </div>
      
      <!-- 数据概览 -->
      <div class="data-overview">
        <h3>数据概览</h3>
        <div class="overview-content">
          <p>当前课程共有 <strong>{{ stats.courseTotalPosts }}</strong> 个帖子，吸引了 <strong>{{ stats.courseTotalParticipants }}</strong> 人参与讨论。</p>
          <p>获得了 <strong>{{ stats.courseTotalPostLikes }}</strong> 次点赞和 <strong>{{ stats.courseTotalPostStars }}</strong> 次收藏。</p>
        </div>
      </div>
      
      <!-- 柱状图 -->
      <div class="chart-container">
        <h3>数据统计图</h3>
        <v-chart 
          class="chart" 
          :option="chartOption" 
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { getCourseStats } from '@/api/public/course/review'
import { getCourseDetail } from '@/api/public/course/course'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

export default {
  name: 'CourseReview',
  components: {
    VChart
  },
  setup() {
    const route = useRoute()
    const loading = ref(false)
    const error = ref('')
    const courseInfo = ref({})
    const stats = ref({
      courseTotalPosts: 0,
      courseTotalParticipants: 0,
      courseTotalPostLikes: 0,
      courseTotalPostStars: 0
    })

    // 从路由参数获取课程ID
    const courseId = computed(() => route.params.courseId)

    // 获取课程详情
    const fetchCourseInfo = async () => {
      try {
        console.log('获取课程信息，课程ID:', courseId.value)
        const response = await getCourseDetail(courseId.value)
        if (response && response.code === 200) {
          courseInfo.value = response.result.course || {}
          console.log('课程信息获取成功:', courseInfo.value)
        }
      } catch (err) {
        console.error('获取课程信息失败:', err)
      }
    }

    // 获取统计数据
    const fetchStats = async () => {
      if (!courseId.value) {
        error.value = '课程ID不存在'
        return
      }

      try {
        loading.value = true
        error.value = ''
        console.log('获取统计数据，课程ID:', courseId.value)
        const response = await getCourseStats(courseId.value)
        
        if (response && response.code === 200) {
          stats.value = response.result
          console.log('统计数据获取成功:', stats.value)
        } else {
          throw new Error(response?.msg || '获取数据失败')
        }
      } catch (err) {
        console.error('获取课程统计数据失败:', err)
        error.value = err.message || '网络错误，请稍后重试'
      } finally {
        loading.value = false
      }
    }

    // 监听课程ID变化
    watch(courseId, (newCourseId) => {
      if (newCourseId) {
        console.log('课程ID变化:', newCourseId)
        fetchCourseInfo()
        fetchStats()
      }
    }, { immediate: true })

    // 图表配置
    const chartOption = computed(() => ({
      title: {
        text: `${courseInfo.value.name || '课程'}数据统计`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          let result = params[0].name + '<br/>'
          params.forEach(param => {
            result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`
          })
          return result
        }
      },
      legend: {
        data: ['帖子数', '参与人数', '点赞数', '收藏数'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['课程数据'],
        axisLabel: {
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '帖子数',
          type: 'bar',
          data: [stats.value.courseTotalPosts],
          barWidth: 'auto', // 自动宽度
          barMaxWidth: '25%', // 最大宽度限制
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '参与人数',
          type: 'bar',
          data: [stats.value.courseTotalParticipants],
          barWidth: 'auto',
          barMaxWidth: '25%',
          itemStyle: {
            color: '#67C23A'
          }
        },
        {
          name: '点赞数',
          type: 'bar',
          data: [stats.value.courseTotalPostLikes],
          barWidth: 'auto',
          barMaxWidth: '25%',
          itemStyle: {
            color: '#E6A23C'
          }
        },
        {
          name: '收藏数',
          type: 'bar',
          data: [stats.value.courseTotalPostStars],
          barWidth: 'auto',
          barMaxWidth: '25%',
          itemStyle: {
            color: '#F56C6C'
          }
        }
      ]
    }))

    return {
      loading,
      error,
      stats,
      courseInfo,
      courseId,
      chartOption,
      fetchStats
    }
  }
}
</script>

<style scoped>
.course-review-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stats-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.course-info {
  text-align: right;
}

.course-name {
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.course-meta {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.course-meta span {
  margin-left: 16px;
}

.course-meta span:first-child {
  margin-left: 0;
}

.course-id {
  color: #909399;
  font-size: 14px;
  background: #f0f2f5;
  padding: 8px 16px;
  border-radius: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-container h3 {
  margin: 0 0 8px 0;
  color: #F56C6C;
  font-size: 18px;
}

.error-container p {
  margin: 0 0 20px 0;
  color: #606266;
}

.retry-btn {
  background: #409EFF;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.retry-btn:hover {
  background: #337ecc;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.data-overview {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.data-overview h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.overview-content p {
  margin: 8px 0;
  color: #606266;
  line-height: 1.6;
}

.overview-content strong {
  color: #409EFF;
  font-weight: 600;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.chart {
  height: 400px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .course-info {
    text-align: center;
  }
  
  .course-meta span {
    display: block;
    margin: 4px 0;
  }
  
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .chart {
    height: 300px;
  }
}
</style>