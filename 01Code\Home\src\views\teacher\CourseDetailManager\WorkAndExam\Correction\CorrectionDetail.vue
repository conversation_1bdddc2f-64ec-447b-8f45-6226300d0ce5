<!-- src\views\teacher\CourseDetailManager\WorkAndExam\Correction\CorrectionDetail.vue -->
<template>
    <div class="correction-page">
        <!-- 顶部导航栏 -->
        <div class="nav-bar">
            <div class="left-section">
                <el-button type="text" @click="$router.back()">
                    <el-icon>
                        <ArrowLeft />
                    </el-icon> 返回
                </el-button>
            </div>
            <div class="center-section">
                <h3>{{ assignmentTitle }}</h3>
            </div>
            <div class="right-section">
                <span class="student-name">{{ student.name }}</span>
                <el-button type="danger" @click="handleReject">打回试卷</el-button>
                <el-button type="primary" @click="handleFinish">批阅完成</el-button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <div class="paper-detail">
                <!-- 单选题 -->
                <div v-if="singleChoiceList.length">
                    <h3>单选题</h3>
                    <div v-for="(q, index) in singleChoiceList" :key="'sc-' + index" class="question-block">
                        <div class="question-header">
                            {{ index + 1 }}. <span v-html="q.content"></span> （{{ q.score }}分）
                        </div>
                        <div class="options">
                            <div v-for="opt in q.options" :key="opt.value" class="option-item">
                                <input type="radio" :name="'single-' + index" :checked="q.studentAnswer === opt.value"
                                    disabled :class="getOptionClass(opt.value, q.studentAnswer, q.correct)" />
                                <label :class="getOptionClass(opt.value, q.studentAnswer, q.correct)">
                                    {{ opt.label }}. {{ opt.text }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 多选题 -->
                <div v-if="multipleChoiceList.length">
                    <h3>多选题</h3>
                    <div v-for="(q, index) in multipleChoiceList" :key="'mc-' + index" class="question-block">
                        <div class="question-header">
                            {{ index + 1 }}. <span v-html="q.content"></span> （{{ q.score }}分）
                        </div>
                        <div class="options">
                            <div v-for="opt in q.options" :key="opt.value" class="option-item">
                                <input type="checkbox" :checked="q.studentAnswer.includes(opt.value)" disabled
                                    :class="getCheckboxClass(opt.value, q.studentAnswer, q.correct)" />
                                <label :class="getCheckboxClass(opt.value, q.studentAnswer, q.correct)">
                                    {{ opt.label }}. {{ opt.text }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 判断题 -->
                <div v-if="trueFalseList.length">
                    <h3>判断题</h3>
                    <div v-for="(q, index) in trueFalseList" :key="'tf-' + index" class="question-block">
                        <div class="question-header">
                            {{ index + 1 }}. <span v-html="q.content"></span> （{{ q.score }}分）
                        </div>
                        <div class="options">
                            <div class="option-item">
                                <input type="radio" :name="'tf-' + index" :checked="q.studentAnswer === true" disabled
                                    :class="getOptionClass(true, q.studentAnswer, q.answer)" />
                                <label :class="getOptionClass(true, q.studentAnswer, q.answer)">正确</label>
                            </div>
                            <div class="option-item">
                                <input type="radio" :name="'tf-' + index" :checked="q.studentAnswer === false" disabled
                                    :class="getOptionClass(false, q.studentAnswer, q.answer)" />
                                <label :class="getOptionClass(false, q.studentAnswer, q.answer)">错误</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 填空题（保持不变） -->
                <div v-if="fillInBlankList.length">
                    <h3>填空题</h3>
                    <div v-for="(q, index) in fillInBlankList" :key="'fb-' + index" class="question-block">
                        <div v-html="q.content"></div>
                        <div class="student-answer">
                            <strong>学生答案：</strong>
                            <div v-if="q.studentAnswer?.length">
                                <div v-for="(ans, i) in q.studentAnswer" :key="i">填空 {{ i + 1 }}：{{ ans }}</div>
                            </div>
                            <div v-else>未作答</div>
                        </div>
                    </div>
                </div>

                <!-- 简答题（保持不变） -->
                <div v-if="shortAnswerList.length">
                    <h3>简答题</h3>
                    <div v-for="(q, index) in shortAnswerList" :key="q.id" class="question-block">
                        <div class="question-content" v-html="q.content"></div>
                        <div class="student-answer">
                            <strong>学生答案：</strong>
                            <div v-html="q.studentAnswer || '未作答'"></div>
                        </div>

                        <el-form label-position="top" class="correction-form">
                            <el-form-item label="评分">
                                <el-input-number v-model="q.teacherScore" :min="0" :max="q.score" size="small"
                                    controls-position="right" />
                            </el-form-item>
                            <el-form-item label="评语">
                                <el-input type="textarea" v-model="q.comment" placeholder="输入评语（可选）" rows="2" />
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </div>

            <!-- 右侧总结栏 -->
            <div class="summary-panel">
                <div><label>总分：</label> <span>{{ totalScore }}</span></div>
                <el-form label-position="top" class="overall-comment">
                    <el-form-item label="总体评语">
                        <el-input type="textarea" v-model="overallComment" placeholder="输入总体评语" rows="4" />
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getSubmitDetail } from '@/api/teacher/correction'

// 路由参数
const route = useRoute()
const assignmentTitle = ref(route.query.title || '试卷标题')
const student = ref({ name: route.query.studentName || '李华' })

// 所有题目列表
const singleChoiceList = ref([])
const multipleChoiceList = ref([])
const trueFalseList = ref([])
const fillInBlankList = ref([])
const shortAnswerList = ref([]) // 简答题含评分

const overallComment = ref('')

// 总分 = 简答题打分之和
const totalScore = computed(() =>
    shortAnswerList.value.reduce((sum, q) => sum + (q.teacherScore || 0), 0)
)

const handleReject = () => {
    ElMessage.info('已打回试卷')
    // TODO: 调用打回接口
}

const handleFinish = () => {
    ElMessage.success('批阅完成')
    // TODO: 遍历简答题，调用 correctShortAnswer 提交每道评分
}

// 选项样式判定函数
function getOptionClass(val, studentAnswer, correct) {
    if (studentAnswer === val) {
        return studentAnswer === correct ? 'correct' : 'incorrect'
    }
    return ''
}
function getCheckboxClass(val, studentAnswers, correctAnswers) {
    if (studentAnswers && studentAnswers.includes(val)) {
        return correctAnswers.includes(val) ? 'correct' : 'incorrect'
    }
    return ''
}

// 获取答题详情（在页面加载时调用）
const loadSubmitDetail = async () => {
    const isExam = route.query.isExam === 'true'
    const submitRecordId = route.query.submitRecordId

    try {
        const res = await getSubmitDetail({
            id: submitRecordId
        }, isExam)

        console.log('[提交详情]', res.result)
    } catch (err) {
        console.error('[获取提交详情失败]', err)
        ElMessage.error('加载提交详情失败')
    }
}

// 工具函数：解析字符串/数组字符串
function parseAnswer(val) {
    if (val === 'true') return true
    if (val === 'false') return false
    try {
        const parsed = JSON.parse(val)
        return Array.isArray(parsed) ? parsed : val
    } catch {
        return val
    }
}

// 模拟选项（后续替换为题库接口返回）
function mockOptions() {
    return [
        { label: 'A', value: 'A', text: '选项 A' },
        { label: 'B', value: 'B', text: '选项 B' },
        { label: 'C', value: 'C', text: '选项 C' },
        { label: 'D', value: 'D', text: '选项 D' }
    ]
}

// 页面加载时调用
onMounted(() => {
    loadSubmitDetail()
})
</script>


<style scoped>
.correction-page {
    background: #fff;
    padding: 24px 32px;
    min-height: 100vh;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: #333;
}

.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    border-bottom: 2px solid #e0e6ed;
    padding-bottom: 12px;
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
}

.left-section,
.center-section,
.right-section {
    flex: 1;
    display: flex;
    align-items: center;
}

.center-section {
    justify-content: center;
    /* font-size: 16px; */
    font-weight: 700;
}

.right-section {
    justify-content: flex-end;
    gap: 16px;
}

.student-name {
    color: #555;
    font-size: 15px;
    font-weight: 500;
    margin-right: 28px;
}

.content-area {
    display: flex;
    gap: 28px;
    align-items: flex-start;
}

.paper-detail {
    flex: 7;
    max-width: 900px;
}

.question-block {
    border: 1.5px solid #d1d9e6;
    padding: 18px 22px;
    margin-bottom: 22px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgb(0 0 0 / 0.05);
    background-color: #fafbfc;
    transition: border-color 0.3s ease;
}

/* .question-block:hover {
    border-color: #409EFF;
    background-color: #f0f6ff;
} */

.question-header {
    font-weight: 700;
    font-size: 17px;
    margin-bottom: 14px;
    color: #34495e;
}

.options {
    margin-left: 24px;
}

.option-item {
    line-height: 28px;
    margin-bottom: 10px;
    user-select: none;
    display: flex;
    align-items: center;
    font-size: 15px;
    cursor: default;
}

.option-item input[type="radio"],
.option-item input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    cursor: default;
    accent-color: #409EFF;
}

.option-item label {
    user-select: none;
    cursor: default;
}

/* 正确选项：绿色，背景微调 */
.correct {
    color: #2ecc71;
    font-weight: 600;
}

.correct label {
    background-color: #e6f4ea;
    padding: 2px 6px;
    border-radius: 4px;
}

/* 错误选项：红色，背景微调 */
.incorrect {
    color: #e74c3c;
    font-weight: 600;
}

.incorrect label {
    background-color: #fdecea;
    padding: 2px 6px;
    border-radius: 4px;
}

.student-answer {
    margin-top: 10px;
    background: #f4f6f9;
    padding: 10px 14px;
    border-left: 5px solid #409EFF;
    font-size: 14px;
    color: #2c3e50;
    border-radius: 4px;
    line-height: 1.5;
}

.correction-form {
    margin-top: 16px;
}

.summary-panel {
    flex: 3;
    border: 1.5px solid #d1d9e6;
    padding: 24px 20px;
    border-radius: 8px;
    background: #f9fbfc;
    box-shadow: 0 1px 8px rgb(0 0 0 / 0.05);
    font-size: 16px;
}

.summary-panel label {
    font-weight: 700;
    margin-right: 12px;
    color: #34495e;
}

.overall-comment {
    margin-top: 20px;
}

.el-input__inner,
.el-input-number__input {
    font-size: 14px;
    padding: 6px 8px;
}

.el-button--danger {
    background-color: #e74c3c;
    border-color: #e74c3c;
}

.el-button--danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.el-button--primary {
    background-color: #409EFF;
    border-color: #409EFF;
}

.el-button--primary:hover {
    background-color: #357ABD;
    border-color: #357ABD;
}
</style>
