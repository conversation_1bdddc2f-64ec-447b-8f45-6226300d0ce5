// 学生端的课程相关的api
import request from '@/api/service'

export function enrollCourse(data) {
  return request({
    url: '/student/course/enrollment',
    method: 'post', 
    data: data 
  });
}

// 获取学生课程列表
export const getStudentCourses = (params) => {
  return request.get('/student/course/my-courses', { params })
}

//获取课程详情（简单）
export const getCourseDetail = (courseId) => {
  return request({
    url: '/course/get',
    method: 'get',
    params: { id: courseId }
  })
}


//退出课程
export const exitStudentCourse = (courseId) => {
  return request({
    url: '/student/course/exit',
    method: 'post',
    data: { courseId }
  })
}
export const getCourseList = (params = {}) => {
  return request({
    url: '/course/list',
    method: 'get',
    params: {
      pageNum : 1,
      pageSize : 10,
      ...params
    }
  });
};

// 获取学生端课程公告列表
export const getStudentNoticeList = (params = {}) => {
  // 确保必需参数存在
  const requestParams = {
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 10,
    ...params
  }

  return request({
    url: '/course/notice/list/resources',
    method: 'get',
    params: requestParams
  }).catch(error => {
    // 如果是500错误，返回Mock数据用于测试
    if (error.response?.status === 500) {
      return {
        code: 200,
        msg: "操作成功（Mock数据）",
        result: {
          records: [
            {
              id: "mock-1",
              courseId: params.courseId || "1",
              noticeType: 0,
              publisherId: "teacher-1",
              title: "欢迎学习本课程",
              content: "<p>欢迎大家学习本课程！请认真阅读课程大纲，按时完成作业。</p>",
              publishTime: Date.now() - 86400000, // 1天前
              updatedTime: Date.now() - 86400000,
              userId: "user-1",
              readCount: 25,
              assignmentId: null,
              examId: null,
              resources: [
                {
                  id: "res-1",
                  noticeId: "mock-1",
                  originalName: "课程大纲.pdf",
                  url: "#",
                  fileType: "pdf",
                  fileSize: 1024000,
                  uploadTime: Date.now() - 86400000,
                  fileExtension: "pdf",
                  sysName: "syllabus.pdf"
                }
              ],
              classId: "class-1"
            },
            {
              id: "mock-2",
              courseId: params.courseId || "1",
              noticeType: 1,
              publisherId: "teacher-1",
              title: "第一次作业通知",
              content: "<p>请大家在本周五之前完成第一次作业，作业内容请参考附件。</p>",
              publishTime: Date.now() - 172800000, // 2天前
              updatedTime: Date.now() - 172800000,
              userId: "user-1",
              readCount: 18,
              assignmentId: "assignment-1",
              examId: null,
              resources: [],
              classId: "class-1"
            },
            {
              id: "mock-3",
              courseId: params.courseId || "1",
              noticeType: 2,
              publisherId: "teacher-1",
              title: "期中考试安排",
              content: "<p>期中考试将于下周三进行，请大家做好复习准备。考试范围为前五章内容。</p>",
              publishTime: Date.now() - 259200000, // 3天前
              updatedTime: Date.now() - 259200000,
              userId: "user-1",
              readCount: 32,
              assignmentId: null,
              examId: "exam-1",
              resources: [
                {
                  id: "res-2",
                  noticeId: "mock-3",
                  originalName: "考试大纲.docx",
                  url: "#",
                  fileType: "docx",
                  fileSize: 512000,
                  uploadTime: Date.now() - 259200000,
                  fileExtension: "docx",
                  sysName: "exam_outline.docx"
                }
              ],
              classId: "class-1"
            }
          ],
          total: 3,
          size: 10,
          current: 1,
          pages: 1
        }
      }
    }

    throw error
  })
};

// 获取课程章节大纲
export const getCourseChapters = (courseId) => {
  return request({
    url: '/course/syllabus/getCourseId',
    method: 'get',
    params: { courseId }
  })
}

export const getChapterResources = (courseId, syllabusId) => {
  return request({
    url: '/course/link/chapter-resources',
    method: 'get',
    params: { 
      courseId,
      syllabusId,
      state: 1 // 学生端只获取已发布的资源
    }
  })
}

// 学生加入班级
export const joinClass = (invitationCode) => {
  return request({
    url: '/class/student/joinclass',
    method: 'post',
    data: { invitationCode }
  });
};