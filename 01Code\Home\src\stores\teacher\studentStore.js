// studentStore.js
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import {
    getStudents,
    getClasses,
    getVerificationCode,
    getCourseQrCode,
    inviteByPhone,
    enrollStudents,
    rejectStudents,
    deleteStudents,
    createClass,
    exportStudentData,
    deleteClass,
    getInvitationCodes
} from '@/api/teacher/student';
import { useRoute } from 'vue-router';
import { useUserStore } from '@/stores/userStore';
import { ElMessage } from 'element-plus';

export const useStudentStore = defineStore('student', () => {
    const students = ref([]);
    const classes = ref(['所有学生']);
    const activeTab = ref('已入班'); // 默认显示已入班标签
    const activeClass = ref('所有学生');
    const searchKeyword = ref('');
    const classSearchKeyword = ref('');
    const selectedStudents = ref([]);
    const selectAll = ref(false);
    const isNewClassMode = ref(false);
    const newClassName = ref('');
    const qrCodeDialogVisible = ref(false);
    const phoneInviteDialogVisible = ref(false);
    const showExportDialog = ref(false);
    const courseCode = ref('');
    const verificationCodeImage = ref('');
    const studentPhone = ref('');
    const verificationCode = ref('');
    const userStore = useUserStore();
    const route = useRoute();
    const teacherId = computed(() => userStore.user.id);
    const classSelectionVisible = ref(false);
    const invitationCodeVisible = ref(false);
    const invitationData = ref({
        courseName: '',
        courseInvitationCode: '',
        className: '',
        classInvitationCode: ''
    });

    // 关键修改1: 重构fetchStudents方法获取所有班级的所有学生
    const fetchStudents = async () => {
        try {
            // 获取所有班级ID（排除"所有学生"）
            const classIds = classes.value
                .filter(cls => typeof cls !== 'string')
                .map(cls => cls.id);

            // 如果没有班级，直接返回空数组
            if (classIds.length === 0) {
                students.value = [];
                return;
            }

            // 并行请求所有班级的学生数据（不传status获取全部状态）
            const requests = classIds.map(classId =>
                getStudents({
                    classId: classId,
                    status: null, // 获取所有状态的学生
                    pageNum: 1,
                    pageSize: 1000
                })
            );

            const responses = await Promise.all(requests);
            // 合并所有班级的学生数据
            students.value = responses.flatMap(res => res);
        } catch (error) {
            console.error('获取学生列表失败:', error);
            ElMessage.error('加载学生列表失败，请重试');
        }
    };

    // 关键修改2: 重构过滤逻辑使用完整数据集
    const filteredStudents = computed(() => {
        return students.value.filter(student => {
            const statusMatch = getStatusDesc(student.status) === activeTab.value;
            const classMatch = activeClass.value === '所有学生' || student.className === activeClass.value;
            const keywordMatch = !searchKeyword.value ||
                (student.studentId && student.studentId.includes(searchKeyword.value)) ||
                (student.studentName && student.studentName.includes(searchKeyword.value));
            return statusMatch && classMatch && keywordMatch;
        });
    });

    const filteredClasses = computed(() => {
        return classes.value.filter(cls => typeof cls !== 'string');
    });

    const classCount = computed(() => classes.value.length - 1); // 排除"所有学生"
    const studentCount = computed(() => students.value.filter(s => s.status === 2).length);
    const reviewToEnrolledCount = computed(() => students.value.filter(s => s.status === 2 && s.joinType === '审核').length);
    const pendingReviewCount = computed(() => students.value.filter(s => s.status === 1).length);
    const invitationToEnrolledCount = computed(() => students.value.filter(s => s.status === 2 && s.joinType === '邀请').length);
    const pendingInvitationCount = computed(() => students.value.filter(s => s.status === 0).length);
    const rejectedCount = computed(() => students.value.filter(s => s.status === 3).length);

    const getStatusDesc = (status) => {
        switch (status) {
            case 0: return '待入班';
            case 1: return '待审核';
            case 2: return '已入班';
            case 3: return '已拒绝/已移除';
            default: return '';
        }
    };

    // 关键修改3: 重构getStudentCount方法使用完整数据集
    const getStudentCount = (className) => {
        let status = null;
        switch (activeTab.value) {
            case '待入班': status = 0; break;
            case '待审核': status = 1; break;
            case '已入班': status = 2; break;
            case '已拒绝/已移除': status = 3; break;
            default: status = null;
        }

        // 使用完整的学生数据集进行统计
        return students.value.filter(student => {
            const statusMatch = status === null ? true : student.status === status;
            const classMatch = className === '所有学生' || student.className === className;
            return statusMatch && classMatch;
        }).length;
    };

    // 确保初始化顺序：先班级后学生
    const initializeData = async () => {
        try {
            await fetchClasses(); // 先获取班级
            await fetchStudents(); // 再获取所有学生
        } catch (error) {
            console.error('初始化数据失败:', error);
        }
    };

    const fetchClasses = async () => {
        try {
            const response = await getClasses(route.params.courseId);
            if (response) {
                const classObjects = response.map(cls => ({
                    id: cls.id,
                    name: cls.name
                }));
                classes.value = ['所有学生', ...classObjects];
            }
        } catch (error) {
            console.error('获取班级列表失败:', error);
        }
    };

    const fetchVerificationCode = async () => {
        const blob = await getVerificationCode();
        verificationCodeImage.value = URL.createObjectURL(blob);
    };

    const fetchCourseQrCode = async () => {
        const blob = await getCourseQrCode({ courseId: route.params.courseId });
        return URL.createObjectURL(blob);
    };

    const refreshVerificationCode = async () => {
        await fetchVerificationCode();
    };

    const inviteStudentByPhone = async () => {
        try {
            await inviteByPhone({
                courseId: route.params.courseId,
                phone: studentPhone.value,
                code: verificationCode.value
            });
            ElMessage.success('邀请发送成功');
            phoneInviteDialogVisible.value = false;
            await fetchStudents(); // 刷新完整学生列表
        } catch (error) {
            ElMessage.error('邀请失败: ' + (error.message || '请重试'));
        }
    };

    const enrollStudentsAction = async () => {
        if (selectedStudents.value.length === 0) {
            ElMessage.warning('请选择要操作的学生');
            return;
        }
        try {
            // 使用响应中的id字段而非studentId
            const studentIds = selectedStudents.value.map(s => s.id);
            const status = 2;
            console.log('调用enrollStudents接口，参数:', studentIds, status);
            await enrollStudents({ studentIds: studentIds, status: status });
            ElMessage.success('操作成功');
            await fetchStudents(); // 强制刷新完整学生列表
            selectedStudents.value = [];
        } catch (error) {
            ElMessage.error('操作失败: ' + (error.message || '请重试'));
        }
    };

    const rejectStudentsAction = async () => {
        if (selectedStudents.value.length === 0) {
            ElMessage.warning('请选择要操作的学生');
            return;
        }
        try {
            const studentIds = selectedStudents.value.map(s => s.id);
            await rejectStudents({ studentIds: studentIds, status: 3 });
            ElMessage.success('拒绝成功');
            await fetchStudents(); // 强制刷新完整学生列表
            selectedStudents.value = [];
        } catch (error) {
            ElMessage.error('拒绝失败: ' + (error.message || '请重试'));
        }
    };

    const removeStudents = async () => {
        if (selectedStudents.value.length === 0) {
            ElMessage.warning('请选择要操作的学生');
            return;
        }
        try {
            // 使用响应中的id字段而非studentId
            const studentIds = selectedStudents.value.map(s => s.id);
            await deleteStudents({ studentIds: studentIds, status: 3 });
            ElMessage.success('删除成功');
            await fetchStudents(); // 强制刷新完整学生列表
            selectedStudents.value = [];
        } catch (error) {
            ElMessage.error('删除失败: ' + (error.message || '请重试'));
        }
    };

    const createNewClass = async () => {
        if (!newClassName.value.trim()) {
            ElMessage.warning('请输入班级名称');
            return;
        }
        if (!route.params.courseId) {
            ElMessage.warning('课程ID无效，请检查');
            return;
        }
        try {
            await createClass({
                name: newClassName.value.trim(),
                courseId: route.params.courseId,
                teacherId: teacherId.value
            });
            ElMessage.success('班级创建成功');
            isNewClassMode.value = false;
            newClassName.value = '';
            await fetchClasses();
            await fetchStudents(); // 刷新完整学生列表
        } catch (error) {
            console.error('创建失败:', error);
            ElMessage.error('创建失败: ' + (error.message || '请重试'));
        }
    };

    // 删除班级
    const deleteClassAction = async (className) => {
        try {
            const classObj = classes.value.find(cls =>
                typeof cls !== 'string' && cls.name === className
            );

            if (!classObj) {
                ElMessage.warning('未找到对应班级');
                return;
            }
            if (activeClass.value === className) {
                activeClass.value = '所有学生';
            }
            await deleteClass(classObj.id);
            ElMessage.success('班级删除成功');
            await fetchClasses();
            await fetchStudents(); // 刷新完整学生列表
        } catch (error) {
            ElMessage.error('删除班级失败: ' + (error.message || '请重试'));
        }
    };

    const exportStudents = async () => {
        try {
            const blob = await exportStudentData({
                courseId: route.params.courseId,
                status: activeTab.value,
                class: activeClass.value === '所有学生' ? null : activeClass.value
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `学生数据_${new Date().toLocaleDateString()}.xlsx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            ElMessage.success('导出成功');
            showExportDialog.value = false;
        } catch (error) {
            ElMessage.error('导出失败: ' + (error.message || '请重试'));
        }
    };

    const showClassSelectionDialog = () => {
        classSelectionVisible.value = true;
    };

    const fetchInvitationCodes = async (classId) => {
        try {
            const response = await getInvitationCodes(classId);
            if (response.code === 200 || response.code === 0) { // 根据实际API响应调整
                invitationData.value = {
                    courseName: response.result.courseName || '当前课程',
                    courseInvitationCode: response.result.courseInvitationCode,
                    className: response.result.className,
                    classInvitationCode: response.result.classInvitationCode ||
                        response.result.invitationCode // 根据实际API字段调整
                };
                invitationCodeVisible.value = true;
                console.log('邀请码数据已设置，弹窗状态:', invitationCodeVisible.value);
            } else {
                throw new Error(response.msg || '获取邀请码失败');
            }
        } catch (error) {
            console.error('获取邀请码失败:', error);
            throw error;
        }
    };

    return {
        students,
        classes,
        activeTab,
        activeClass,
        searchKeyword,
        classSearchKeyword,
        selectedStudents,
        selectAll,
        isNewClassMode,
        newClassName,
        qrCodeDialogVisible,
        phoneInviteDialogVisible,
        showExportDialog,
        courseCode,
        verificationCodeImage,
        studentPhone,
        verificationCode,
        filteredStudents,
        classCount,
        studentCount,
        reviewToEnrolledCount,
        pendingReviewCount,
        invitationToEnrolledCount,
        pendingInvitationCount,
        rejectedCount,
        initializeData,
        fetchStudents,
        fetchClasses,
        fetchVerificationCode,
        fetchCourseQrCode,
        refreshVerificationCode,
        inviteStudentByPhone,
        enrollStudents: enrollStudentsAction,
        rejectStudents: rejectStudentsAction,
        removeStudents,
        createNewClass,
        exportStudents,
        deleteClass: deleteClassAction,
        classSelectionVisible,
        invitationCodeVisible,
        invitationData,
        showClassSelectionDialog,
        fetchInvitationCodes,
        filteredClasses,
        getStudentCount
    };
}); 