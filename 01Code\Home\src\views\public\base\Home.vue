<template>
  <div class="home-header">
    <!-- 学校名称与专业标题 -->
    <div class="header-01">
      <div class="pro-info">
        <p class="school-name">桂林电子科技大学</p>
        <h1 class="pro-title">数字媒体技术专业</h1>
      </div>
      <div class="visits">
        <div class="visit-today">
          <p class="visit-label">今日访问</p>
          <p class="visit-number">60</p>
        </div>
        <div class="visit-total">
          <p class="visit-label">总访问</p>
          <p class="visit-number">12,345</p>
        </div>
        <div class="visit-add">
          <p class="visit-label">较上周</p>
          <p class="visit-number">+30</p>
        </div>
      </div>
    </div>
    <!-- 数据统计模块 -->
    <div class="header-02">
      <button class="pro-btn">专业大图谱 →</button>
      <div class="stats">
        <div class="stat-item">
          <div>
            <p class="stat-number">49</p>
            <p class="stat-label">门</p>
          </div>
          <p class="stat-label">全部课程</p>
        </div>
        <div class="stat-item">
          <div>
            <p class="stat-number">2,988</p>
            <p class="stat-label">个</p>
          </div>
          <p class="stat-label">知识点</p>
        </div>
        <div class="stat-item">
          <div>
            <p class="stat-number">49</p>
            <p class="stat-label">门</p>
          </div>
          <p class="stat-label">课程知识图谱</p>
        </div>
      </div>
    </div>
    <!--课程模块-->
    <div class="header-03">
      <!-- 课程滚动容器 -->
      <div class="course-scroll" ref="courseScrollRef">
        <CourseCard 
          v-for="course in allCoursesSortedBySemester" 
          :key="course.id" 
          :course="course" 
          :data-course-id="course.id" 
          :data-semester="course.semester"
          @click="navigateToCourse(course.id)"
        />
      </div>
      <!-- 学期导航 -->
      <div class="semester-nav">
        <div 
          v-for="semester in semesters" 
          :key="semester" 
          class="semester-item"
          :class="{ active: activeSemester === semester }" 
          @click="handleSemesterClick(semester)"
        >
          <div class="indicator"></div>
          {{ semester }}
        </div>
      </div>
    </div>
  </div>

  <!--专业建设方案-->
  <div class="home-plan">
    <div class="smartImage"></div>
    <MajorConstruction :majorId="currentMajorId" :showImage="false" @view-details="handleViewDetails" />
  </div>

  <!--三大方向-->
  <div class="home-Directions">
    <ThreeDirections @direction-click="handleDirectionClick" />
  </div>

  <!--个性化学习路径推荐-->
  <div class="home-recommend">
    <PersonalizedPath />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth/auth';
import { getCourseList } from '@/api/student/course';
import CourseCard from '@/views/public/base/components/CourseCard.vue';
import MajorConstruction from '@/views/public/base/components/MajorConstruction.vue';
import ThreeDirections from '@/views/public/base/components/ThreeDirections.vue';
import PersonalizedPath from '@/views/public/base/components/PersonalizedPath.vue';
import { ElMessage } from 'element-plus';

// 状态定义
const currentMajorId = ref('1'); // 当前专业ID
const semesters = ref(['第一学期', '第二学期', '第三学期', '第四学期', '第五学期', '第六学期', '第七学期', '第八学期']);
const activeSemester = ref('第一学期');
const courseScrollRef = ref(null);
const isManualScroll = ref(false); // 是否手动滚动
const courseList = ref([]);
const loading = ref(false);
const userStore = useAuthStore();
const router = useRouter();

// 获取课程数据
const fetchCourses = async () => {
  try {
    loading.value = true;
    
    const res = await getCourseList();
    if (res.code === 200) {
      courseList.value = res.result.records.map(course => ({
        id: course.id,
        cover: course.courseCover || require('@/assets/img/Course/courseCover/cover1.png'),
        title: course.name,
        type: course.courseType,
        nature: course.subjectCategory,
        semester: course.semester || '第一学期', // 确保有默认学期
      }));
    }
  } catch (error) {
    console.error('获取课程列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 计算属性：按学期排序的所有课程（保持同一行显示）
const allCoursesSortedBySemester = computed(() => {
  const sorted = [...courseList.value];
  sorted.sort((a, b) => {
    const semesterOrder = semesters.value;
    return semesterOrder.indexOf(a.semester) - semesterOrder.indexOf(b.semester);
  });
  return sorted;
});

// 监听滚动事件 - 自动切换学期高亮
const handleScroll = () => {
  if (!courseScrollRef.value || isManualScroll.value) return;

  const container = courseScrollRef.value;
  const containerRect = container.getBoundingClientRect();
  const containerCenterX = containerRect.left + containerRect.width / 2;

  // 找到可视区域中心最接近的课程卡片
  const cards = Array.from(container.children);
  let closestCard = null;
  let minDistance = Infinity;

  cards.forEach(card => {
    const cardRect = card.getBoundingClientRect();
    const cardCenterX = cardRect.left + cardRect.width / 2;
    const distance = Math.abs(cardCenterX - containerCenterX);

    if (distance < minDistance) {
      minDistance = distance;
      closestCard = card;
    }
  });

  // 根据最接近的卡片切换活动学期
  if (closestCard) {
    const semester = closestCard.dataset.semester;
    if (semester && activeSemester.value !== semester) {
      activeSemester.value = semester;
    }
  }
};

// 拖拽滚动实现
const startDrag = (e) => {
  if (!courseScrollRef.value) return;
  
  const slider = courseScrollRef.value;
  const startX = e.pageX || e.touches?.[0]?.pageX;
  const scrollLeft = slider.scrollLeft;
  const startTime = Date.now();

  // 拖动中
  const onDragMove = (e) => {
    e.preventDefault(); // 防止拖动时选中文本
    const x = e.pageX || e.touches?.[0]?.pageX;
    const walk = (x - startX) * 1.2; // 增加一点惯性效果
    slider.scrollLeft = scrollLeft - walk;
  };

  // 结束拖动
  const endDrag = () => {
    window.removeEventListener('mousemove', onDragMove);
    window.removeEventListener('mouseup', endDrag);
    window.removeEventListener('touchmove', onDragMove);
    window.removeEventListener('touchend', endDrag);
  };

  // 添加事件监听
  window.addEventListener('mousemove', onDragMove);
  window.addEventListener('mouseup', endDrag);
  window.addEventListener('touchmove', onDragMove);
  window.addEventListener('touchend', endDrag);
};

// 滚动到指定学期的第一个课程
const scrollToSemester = (semester) => {
  if (!courseScrollRef.value) return;

  isManualScroll.value = true;
  const container = courseScrollRef.value;
  const cards = Array.from(container.children);

  // 找到该学期的第一个课程卡片
  const firstCardOfSemester = cards.find(card => {
    return card.dataset.semester === semester;
  });

  if (firstCardOfSemester) {
    // 计算滚动位置（使卡片居中显示）
    const containerRect = container.getBoundingClientRect();
    const cardRect = firstCardOfSemester.getBoundingClientRect();
    const scrollPosition = firstCardOfSemester.offsetLeft - (containerRect.width - cardRect.width) / 2;

    container.scrollTo({
      left: scrollPosition,
      behavior: 'smooth'
    });

    // 1秒后恢复自动检测（等待滚动结束）
    setTimeout(() => {
      isManualScroll.value = false;
    }, 1000);
  }
};

// 学期点击处理
const handleSemesterClick = (semester) => {
  if (activeSemester.value === semester) return;
  activeSemester.value = semester;
  scrollToSemester(semester);
};

// 组件挂载时初始化
onMounted(() => {
  fetchCourses();
  
  if (courseScrollRef.value) {
    // 添加拖拽事件
    courseScrollRef.value.addEventListener('mousedown', startDrag);
    courseScrollRef.value.addEventListener('touchstart', startDrag);
    // 添加滚动事件
    courseScrollRef.value.addEventListener('scroll', handleScroll);
  }
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  if (courseScrollRef.value) {
    courseScrollRef.value.removeEventListener('mousedown', startDrag);
    courseScrollRef.value.removeEventListener('touchstart', startDrag);
    courseScrollRef.value.removeEventListener('scroll', handleScroll);
  }
});

// 其他原有方法
const handleViewDetails = (type) => {
  console.log(`查看${type}详情`);
  // 跳转到详情页的逻辑
};

const handleDirectionClick = (direction) => {
  console.log(`点击了${direction}方向`);
  if (direction) {
    router.push(`/three-direction/${direction}`);
  }
};

const navigateToCourse = (courseId) => {
  console.log('当前用户状态:', userStore.user);
  if (!userStore.user) {
    console.log('触发未登录提示');
    ElMessage.warning('请先登录后再查看课程详情');
    return;
  }
  router.push({ name: 'course-detail', params: { courseId } });
};
</script>

<style lang="scss" scoped>
@use '@/styles/public/base/home' as *;
</style>