<!--src\views\teacher\CourseDetailManager\StudentManagement\components\ExportDataDialog.vue-->
<template>
  <el-dialog 
    :model-value="visible" 
    title="学生数据统计" 
    width="40%" 
    @update:model-value="updateVisible"
  >
    <div class="export-stats-container">
      <!-- 已入班 -->
      <div class="stat-section">
        <h3>已入班</h3>
        <div class="stat-numbers">
          <div class="stat-group">
            <div class="stat-number">{{ classCount }}</div>
            <div class="stat-label">班级数</div>
          </div>
          <div class="vertical-line"></div>
          <div class="stat-group">
            <div class="stat-number">{{ studentCount }}</div>
            <div class="stat-label">学生数</div>
          </div>
        </div>
      </div>

      <!-- 审核入班 -->
      <div class="stat-section">
        <h3>审核入班</h3>
        <div class="chart-container">
          <div class="pie-chart" ref="pieChart1"></div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#4CAF50' }"></span>
              <span>已通过 {{ reviewToEnrolledCount }}人</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#FF5722' }"></span>
              <span>待审核 {{ pendingReviewCount }}人</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#FF0000' }"></span>
              <span>已拒绝 {{ rejectedCount }}人</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 邀请入班 -->
      <div class="stat-section">
        <h3>邀请入班</h3>
        <div class="chart-container">
          <div class="pie-chart" ref="pieChart2"></div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#4CAF50' }"></span>
              <span>已通过 {{ invitationToEnrolledCount }}人</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#FF5722' }"></span>
              <span>待入班 {{ pendingInvitationCount }}人</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#FF0000' }"></span>
              <span>已拒绝 {{ rejectedCount }}人</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 导出详情按钮 -->
      <div class="export-details-btn">
        <el-button type="primary" @click="exportDetails">导出详情</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    classCount: {
      type: Number,
      default: 0
    },
    studentCount: {
      type: Number,
      default: 0
    },
    reviewToEnrolledCount: {
      type: Number,
      default: 0
    },
    pendingReviewCount: {
      type: Number,
      default: 0
    },
    invitationToEnrolledCount: {
      type: Number,
      default: 0
    },
    pendingInvitationCount: {
      type: Number,
      default: 0
    },
    rejectedCount: {
      type: Number,
      default: 0
    }
  },
  emits: ['update:visible', 'export-details'],
  setup(props, { emit }) {
    const pieChart1 = ref(null);
    const pieChart2 = ref(null);
    const chartInstances = ref({});
    const initialized = ref(false);

    // 监听 visible 变化，控制图表初始化/销毁
    watch(() => props.visible, (val) => {
      if (val && !initialized.value) {
        initialized.value = true;
        nextTick(() => {
          initCharts();
        });
      } else if (!val) {
        Object.values(chartInstances.value).forEach(chart => chart.dispose());
        chartInstances.value = {};
        initialized.value = false;
      }
    });

    // 初始化图表
    const initCharts = () => {
      // 饼图1：审核入班
      if (pieChart1.value) {
        const chart = echarts.init(pieChart1.value);
        chart.setOption({
          series: [
            {
              type: 'pie',
              radius: ['40%', '60%'],
              center: ['50%', '50%'],
              data: [
                { value: props.reviewToEnrolledCount, name: '已通过', itemStyle: { color: '#4CAF50' } },
                { value: props.pendingReviewCount, name: '待审核', itemStyle: { color: '#FF5722' } },
                { value: props.rejectedCount, name: '已拒绝', itemStyle: { color: '#FF0000' } }
              ],
              label: { show: false },
            }
          ]
        });
        chartInstances.value.chart1 = chart;
      }

      // 饼图2：邀请入班
      if (pieChart2.value) {
        const chart = echarts.init(pieChart2.value);
        chart.setOption({
          series: [
            {
              type: 'pie',
              radius: ['40%', '60%'],
              center: ['50%', '50%'],
              data: [
                { value: props.invitationToEnrolledCount, name: '已通过', itemStyle: { color: '#4CAF50' } },
                { value: props.pendingInvitationCount, name: '待入班', itemStyle: { color: '#FF5722' } },
                { value: props.rejectedCount, name: '已拒绝', itemStyle: { color: '#FF0000' } }
              ],
              label: { show: false },
            }
          ]
        });
        chartInstances.value.chart2 = chart;
      }
    };

    // 窗口resize时自适应
    onMounted(() => {
      const resizeHandler = () => {
        Object.values(chartInstances.value).forEach(chart => chart.resize());
      };
      window.addEventListener('resize', resizeHandler);
      return () => {
        window.removeEventListener('resize', resizeHandler);
      };
    });

    // 关闭弹窗
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 导出详情
    const exportDetails = () => {
      emit('export-details');
    };

    return {
      pieChart1,
      pieChart2,
      updateVisible,
      exportDetails
    };
  }
};
</script>

<style scoped>
.export-stats-container {
  padding: 20px;
}
.stat-section {
  margin-bottom: 24px;
}
.stat-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}
.stat-numbers {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
}
.stat-group {
  text-align: center;
}
.stat-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #409EFF;
}
.stat-label {
  font-size: 12px;
  color: #999;
}
.vertical-line {
  width: 1px;
  height: 30px;
  background-color: #ebeef5;
}
.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.pie-chart {
  width: 120px;
  height: 120px;
  margin-bottom: 8px;
}
.chart-legend {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #666;
}
.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%; /* 圆形 */
}
.export-details-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>