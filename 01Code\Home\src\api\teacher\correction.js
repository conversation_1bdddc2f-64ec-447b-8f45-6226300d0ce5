// src/api/teacher/correction.js
import request from '@/api/service'

/** -------------------- Paper-Exam-01：催交考试 -------------------- **/

// 催交考试或作业
export function urgeSubmitExam(data) {
    return request.post('/exam/urge-submit-exam', data)
}

/** -------------------- Paper-Exam-02：学生考试接口（教师用不到） -------------------- **/
// 这里不封装 GET /student/exam/available 等学生接口


/** -------------------- Paper-Exam-03：教师批改考试题目 -------------------- **/

// 批改简答题
export function correctShortAnswer(data) {
    return request.post('/api/teacher/exam/correct-short-answer', data)
}

// 修改考试中某一题得分
export function updateQuestionScore(data) {
    return request.post('/api/teacher/exam/update-score', data)
}


/** -------------------- Paper-Exam-04：导出考试成绩 -------------------- **/

// 导出某考试某班级提交记录 Excel
export function exportSubmitRecords(params) {
    return request.get('/api/teacher/exam-export/submit-records', { params })
}


/** -------------------- Paper-Exam-05：教师查看考试 -------------------- **/

// 发布考试成绩（设置 isPublishScore 为 true）
export function publishExamScore(data) {
    return request.post('/api/teacher/exam/publish-score', data)
}

// 获取学生作答详情（根据提交记录 ID）
export function getSubmitDetail(params, isExam = true) {
    const url = isExam
        ? '/api/teacher/exam/submit-detail'
        : '/api/teacher/assignment/submit-detail'

    return request.get(url, { params })
}
// 分页查看某考试某班级学生提交记录
export function getExamSubmitRecords(params) {
    return request.get('/api/teacher/exam/submit-records/by-class', { params })
}

//分页查看某作业某班级学生提交记录
export function getAssignmentSubmitRecords(params) {
    return request.get('/api/teacher/assignment/submit-records/by-class', { params })
}
