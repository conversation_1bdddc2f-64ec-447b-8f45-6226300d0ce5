<!-- src\views\public\course\Course_Center\Chapter\index.vue -->
<template>
  <div class="chapter-container">
    <!-- 右侧内容 -->
    <div class="chapter-content-wrapper">
      <div class="chapter-content">
        <div v-if="loading" class="loading">
          <i class="el-icon-loading"></i> 加载中...
        </div>
        
        <div v-else class="chapter-list">
          <chapterNode
            v-for="(chapter, index) in filteredChapters"
            :key="index"
            :node="chapter"
            :depth="0"
            :search-query="searchQuery"
            :anchor-id="`chapter-${chapter.id}`"
            @preview-resource="handlePreview"
            @video-clicked="handleVideoClicked"
          />
        </div>
        
        <div v-if="!loading && filteredChapters.length === 0" class="no-results">
          <i class="fa fa-search-minus"></i> 没有找到匹配的内容
        </div>

        <!-- 引入文件预览弹窗 -->
        <FilePreviewDialog
          :visible="isPreviewVisible && previewFile" 
          :file="previewFile"
          @close="isPreviewVisible = false"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getCourseChapters } from '@/api/student/course'
import chapterNode from './chapterNode.vue'
import FilePreviewDialog from '@/components/FilePreviewDialog.vue'

const route = useRoute()
const courseId = route.params.courseId

// 数据状态
const loading = ref(true)
const chapters = ref([])
const searchQuery = ref('')
const isPreviewVisible = ref(false)
const previewFile = ref(null)
const watchedVideos = ref(new Set()) // 存储已观看的视频ID

// 获取课程章节数据
const fetchChapters = async () => {
  try {
    loading.value = true
    const res = await getCourseChapters(courseId)
    
    if (res.code === 200) {
      chapters.value = formatChapters(res.result || [])
    } else {
      console.error('获取章节失败:', res.msg)
    }
  } catch (error) {
    console.error('请求章节出错:', error)
  } finally {
    loading.value = false
  }
}

// 格式化章节数据
const formatChapters = (nodes) => {
  return nodes.map(node => ({
    ...node,
    children: node.children ? formatChapters(node.children) : []
  }))
}

// 过滤章节列表
const filteredChapters = computed(() => {
  if (!searchQuery.value.trim()) return chapters.value
  
  function searchInNode(node) {
    const isNodeMatch = node.title.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    let childrenMatch = []
    if (node.children && node.children.length > 0) {
      childrenMatch = node.children
        .map(child => searchInNode(child))
        .filter(matched => matched !== null)
    }
    
    if (isNodeMatch || childrenMatch.length > 0) {
      return {
        ...node,
        children: childrenMatch.length > 0 ? childrenMatch : node.children
      }
    }
    
    return null
  }
  
  return chapters.value
    .map(chapter => searchInNode(chapter))
    .filter(chapter => chapter !== null)
})

// 处理预览请求
const handlePreview = (resource) => {
  previewFile.value = resource
  isPreviewVisible.value = true
}

// 处理视频点击事件
const handleVideoClicked = (videoId) => {
  watchedVideos.value.add(videoId)
}

onMounted(() => {
  fetchChapters()
})
</script>

<style lang="scss" scoped>
.chapter-container {
  background: white;
  border-radius: 8px;
  height: 93vh;
}

.chapter-content-wrapper {
  padding: 30px;
}

.chapter-content {
  .chapter-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .no-results {
    text-align: center;
    padding: 40px 0;
    color: #6c757d;
    font-size: 14px;
    
    i {
      margin-right: 8px;
    }
  }
}

.loading {
  padding: 20px;
  text-align: center;
  color: #666;
}

.el-icon-loading {
  margin-right: 8px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>