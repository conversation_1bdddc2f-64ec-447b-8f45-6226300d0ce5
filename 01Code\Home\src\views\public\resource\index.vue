<template>
  <!-- 数媒资源页面 -->
  <div class="resource-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>数媒资源</h1>
    </div>

    <!-- 主内容区 -->
    <div class="resource-container">
      <div class="navigation-sidebar">
        <LeftNav @nav-click="handleNavigationClick" />
      </div>
      <div class="main-content">
        <ResourceContent
          :active-type="currentTab"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import LeftNav from './components/LeftNav.vue'
import ResourceContent from './components/ResourceContent.vue'

// 当前激活的标签页
const currentTab = ref('digital-media')

// 处理导航点击
const handleNavigationClick = (tabKey) => {
  currentTab.value = tabKey
}
</script>

<style lang="scss" scoped>
$border-radius: 2vw;
$primary-color: #8a6de3;
$course-tabs: #f3ecff;
$course-tabs-solid: #d6beff;

/* 整个页面背景 */
.resource-page {
  position: relative;
  padding-top: 10vw;
  padding-left: 3vw;
  padding-right: 3vw;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
    z-index: -1;
  }

  .page-header {
    color: white;
    margin-bottom: 2vw;
    display: flex;
    justify-content: space-between;

    h1 {
      font-size: 3rem;
      font-weight: 400;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .page-header-desc {
      display: flex;
      margin-top: 5vw;
      gap: 2vw;

      .part {
        display: flex;
        flex-direction: column;
        align-items: end;
        gap: 1vw;

        div {
          display: flex;
          align-items: flex-end;
          justify-content: center;

          h1 {
            margin: 0;
            line-height: 1;
            font-size: 3rem;
          }

          p {
            margin: 0 0 0 0.2em;
            line-height: 1;
            align-self: flex-end;
            font-size: 1rem;
          }
        }
      }
    }
  }
}

.resource-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #f5f7fa;
  margin-top: 5vw;

  .navigation-sidebar {
    width: fit-content;
  }

  .main-content {
    flex: 1;
    height: 100%;
    overflow-y: auto;


    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
