<!--src\views\teacher\CourseDetailManager\StudentManagement\index.vue-->
<template>
  <div class="main-content">
    <!-- 顶部操作栏 -->
    <div class="header">
      <div class="header-left">
        <AddStudentDropdown @add-student-command="handleAddStudentCommand" />
      </div>
      <div class="header-right">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入学号、姓名"
          class="search-input"
          suffix-icon="el-icon-search"
        ></el-input>
      </div>
    </div>

    <!-- 内容区 -->
    <div class="content">
      <!-- 标签页和导出按钮 -->
      <div class="tabs-export-container">
        <div class="tabs-container">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="已入班" name="已入班"></el-tab-pane>
            <el-tab-pane label="待入班" name="待入班"></el-tab-pane>
            <el-tab-pane label="待审核" name="待审核"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="export-container">
          <el-button @click="showExportDialog = true">导出数据</el-button>
        </div>
      </div>

      <!-- 下方内容区 -->
      <div class="lower-content">
        <!-- 左侧班级管理 -->
        <div class="left-panel">
          <ClassManagement 
           :class-search-keyword="classSearchKeyword" 
           :class-names="classNames"
           :is-new-class-mode="isNewClassMode"
           :new-class-name="newClassName"
           :students="students"
           :active-class="activeClass"
           @update:class-search-keyword="classSearchKeyword = $event"
           @toggle-new-class-mode="isNewClassMode = !isNewClassMode" 
           @save-class="saveClass"
           @cancel-class="cancelClass"
           @update:new-class-name="newClassName = $event"
           @class-selected="activeClass = $event"
           @delete-class="deleteClass"
         />
        </div>

        <!-- 竖直分割线 -->
        <div class="vertical-divider"></div>

        <!-- 右侧学生表格 -->
        <div class="right-panel">
          <div class="table-container">
            <StudentTable 
              :filtered-students="filteredStudents"
              :select-all="selectAll"
              :selected-students="selectedStudents"
              @handle-selection-change="handleSelectionChange"
            />
            <!-- 操作按钮 -->
            <div class="table-actions" v-if="activeTab === '待入班' || activeTab === '待审核'">
              <el-button type="primary" @click="changeToEnrolled">通过</el-button>
              <el-button type="danger" @click="rejectStudents">拒绝</el-button>
            </div>
            <div class="table-actions" v-if="activeTab === '已入班'">
              <el-button type="danger" @click="deleteSelectedStudents">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 二维码邀请对话框 -->
    <QRCodeDialog 
      :visible="qrCodeDialogVisible" 
      :course-code="courseCode"
      :qr-code-image="qrCodeImage"
      @update:visible="qrCodeDialogVisible = $event"
    />

    <!-- 手机号邀请对话框 -->
    <PhoneInviteDialog 
      :visible="phoneInviteDialogVisible" 
      :verification-code-image="verificationCodeImage"
      @update:visible="phoneInviteDialogVisible = $event"
      @invite-submitted="handleInviteSubmitted"
      @refresh-verification="refreshVerificationCode"
    />

    <!-- 班级选择对话框 -->
    <ClassSelectionDialog 
      :visible="studentStore.classSelectionVisible"
      :classes="studentStore.filteredClasses"
      @update:visible="val => studentStore.classSelectionVisible = val"
      @close="studentStore.classSelectionVisible = false"
      @confirm="handleClassSelected"
    />

    <!-- 邀请码对话框 -->
    <InvitationCodeDialog 
      :visible="studentStore.invitationCodeVisible"
      :course-name="studentStore.invitationData.courseName"
      :course-invitation-code="studentStore.invitationData.courseInvitationCode"
      :class-name="studentStore.invitationData.className"
      :class-invitation-code="studentStore.invitationData.classInvitationCode"
      @update:visible="val => studentStore.invitationCodeVisible = val"
    />

    <!-- 导出数据对话框 -->
    <ExportDataDialog 
      :visible="showExportDialog" 
      :class-count="classCount"
      :student-count="studentCount"
      :review-to-enrolled-count="reviewToEnrolledCount"
      :pending-review-count="pendingReviewCount"
      :invitation-to-enrolled-count="invitationToEnrolledCount"
      :pending-invitation-count="pendingInvitationCount"
      :rejected-count="rejectedCount"
      @update:visible="showExportDialog = $event"
      @export-details="exportDetails"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { ElInput, ElButton, ElTabs, ElTabPane, ElMessage } from 'element-plus';
import { useStudentStore } from '@/stores/teacher/studentStore';
import AddStudentDropdown from './components/AddStudentDropdown.vue';
import QRCodeDialog from './components/QRCodeDialog.vue';
import ExportDataDialog from './components/ExportDataDialog.vue';
import ClassManagement from './components/ClassManagement.vue';
import StudentTable from './components/StudentTable.vue';
import PhoneInviteDialog from './components/PhoneInviteDialog.vue';
import ClassSelectionDialog from './components/ClassSelectionDialog.vue';
import InvitationCodeDialog from './components/InvitationCodeDialog.vue';

export default {
  components: {
    ElInput,
    ElButton,
    ElTabs,
    ElTabPane,
    AddStudentDropdown,
    QRCodeDialog,
    ExportDataDialog,
    ClassManagement,
    StudentTable,
    PhoneInviteDialog,
    ClassSelectionDialog,
    InvitationCodeDialog
  },
  setup() {
     const studentStore = useStudentStore();
    const qrCodeImage = ref('');

    // 初始化数据 - 使用正确的初始化顺序
    onMounted(async () => {
      await studentStore.initializeData()
    })

    // 监听二维码对话框打开
    watch(() => studentStore.qrCodeDialogVisible, async (visible) => {
      if (visible) {
        // 获取课程二维码
        qrCodeImage.value = await studentStore.fetchCourseQrCode();
      }
    });

    // 监听 activeClass 和 activeTab 的变化
    watch([() => studentStore.activeClass, () => studentStore.activeTab], async () => {
      await studentStore.fetchStudents();
    });

    // 处理选择变化
    const handleSelectionChange = (selection) => {
      studentStore.selectedStudents = selection;
      studentStore.selectAll = selection.length === studentStore.filteredStudents.length;
    };

    // 处理添加学生命令
    const handleAddStudentCommand = (command) => {
      if (command === 'qrCode') {
        studentStore.qrCodeDialogVisible = true;
      } else if (command === 'phone') {
        studentStore.phoneInviteDialogVisible = true;
      } else if (command === 'code') {
        studentStore.showClassSelectionDialog();
      }
    };

    // 保存班级
    const saveClass = () => {
      studentStore.createNewClass();
    };

    // 取消班级
    const cancelClass = () => {
      studentStore.newClassName = '';
      studentStore.isNewClassMode = false;
    };

    const deleteClass = (className) => {
        studentStore.deleteClass(className);
    };

    // 提交邀请
    const handleInviteSubmitted = () => {
      studentStore.inviteStudentByPhone();
    };

    // 导出详情
    const exportDetails = () => {
      studentStore.exportStudents();
    };

    const isNewClassMode = computed({
      get: () => studentStore.isNewClassMode,
      set: (value) => studentStore.isNewClassMode = value
    });

    const handleClassSelected = async (selectedClass) => {
      try {
        await studentStore.fetchInvitationCodes(selectedClass.id);
        console.log('弹窗状态:', studentStore.invitationCodeVisible);
      } catch (error) {
        ElMessage.error('获取邀请码失败: ' + (error.message || '请重试'));
      }
    };


    const filteredClasses = computed(() => {
      return studentStore.classes.filter(cls => typeof cls !== 'string');
    });
    return {
      // 状态
      searchKeyword: computed({
        get: () => studentStore.searchKeyword,
        set: (value) => studentStore.searchKeyword = value
      }),
      classSearchKeyword: computed({
        get: () => studentStore.classSearchKeyword,
        set: (value) => studentStore.classSearchKeyword = value
      }),
      students: computed(() => studentStore.students),
      classNames: computed(() => studentStore.classes.map(cls => typeof cls === 'string' ? cls : cls.name)),
      activeTab: computed({
        get: () => studentStore.activeTab,
        set: (value) => studentStore.activeTab = value
      }),
      selectedStudents: computed(() => studentStore.selectedStudents),
      selectAll: computed(() => studentStore.selectAll),
      isNewClassMode,
      newClassName: computed({
        get: () => studentStore.newClassName,
        set: (value) => studentStore.newClassName = value
      }),
      activeClass: computed({
        get: () => studentStore.activeClass,
        set: (value) => studentStore.activeClass = value
      }),
      qrCodeDialogVisible: computed({
        get: () => studentStore.qrCodeDialogVisible,
        set: (value) => studentStore.qrCodeDialogVisible = value
      }),
      phoneInviteDialogVisible: computed({
        get: () => studentStore.phoneInviteDialogVisible,
        set: (value) => studentStore.phoneInviteDialogVisible = value
      }),
      showExportDialog: computed({
        get: () => studentStore.showExportDialog,
        set: (value) => studentStore.showExportDialog = value
      }),
      courseCode: computed(() => studentStore.courseCode),
      verificationCodeImage: computed(() => studentStore.verificationCodeImage),
      studentPhone: computed({
        get: () => studentStore.studentPhone,
        set: (value) => studentStore.studentPhone = value
      }),
      verificationCode: computed({
        get: () => studentStore.verificationCode,
        set: (value) => studentStore.verificationCode = value
      }),

      // 计算属性
      filteredStudents: computed(() => studentStore.filteredStudents),
      classCount: computed(() => studentStore.classCount),
      studentCount: computed(() => studentStore.studentCount),
      reviewToEnrolledCount: computed(() => studentStore.reviewToEnrolledCount),
      pendingReviewCount: computed(() => studentStore.pendingReviewCount),
      invitationToEnrolledCount: computed(() => studentStore.invitationToEnrolledCount),
      pendingInvitationCount: computed(() => studentStore.pendingInvitationCount),
      rejectedCount: computed(() => studentStore.rejectedCount),

      // 方法
      changeToEnrolled: () => studentStore.enrollStudents(),
      rejectStudents: () => studentStore.rejectStudents(),
      deleteSelectedStudents: () => studentStore.removeStudents(),
      handleAddStudentCommand,
      saveClass,
      cancelClass,
      handleSelectionChange,
      handleInviteSubmitted,
      exportDetails,
      refreshVerificationCode: () => studentStore.refreshVerificationCode(),
      qrCodeImage,
      deleteClass,
      studentStore,
      handleClassSelected
    }
  }
}
</script>
<style scoped>
.main-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: white;
  padding: 15px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  height: 60px;
  background-color: #fff;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input {
  width: 240px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  background-color: #fff;
  margin: 0;
}

.tabs-export-container {
  display: flex;
  padding-left: 15px;
  padding-right: 15px;
}

.tabs-container {
  flex: 1;
}

.export-container {
  margin-left: 10px;
}

.lower-content {
  display: flex;
  height: calc(100% - 60px);
  overflow-y: hidden;
}

.left-panel {
  width: 250px;
  padding-left: 15px;
  padding-right: 15px;
  overflow-y: hidden;
}

.right-panel {
  flex: 1;
  padding: 15px;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
}

.vertical-divider {
  width: 1px;
  background-color: #ebeef5;
  height: calc(100% - 30px);
}

.table-container {
  flex: 1;
  overflow-y: auto;
  height: 100%;
  min-height: 200px;
}

.table-actions {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>