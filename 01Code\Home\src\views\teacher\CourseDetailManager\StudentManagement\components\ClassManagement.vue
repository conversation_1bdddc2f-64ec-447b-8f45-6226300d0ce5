<!--src\views\teacher\CourseDetailManager\StudentManagement\components\ClassManagement.vue-->
<template>
  <div class="class-management">
    <div class="class-search">
      <el-input
        :model-value="classSearchKeyword"
        @update:model-value="$emit('update:class-search-keyword', $event)"
        placeholder="请输入班级名称"
        class="class-search-input"
        suffix-icon="el-icon-search"
      ></el-input>
    </div>

    <div class="class-list">
      <div 
        class="class-item" 
        :class="{ selected: className === activeClass }"
        v-for="className in filteredClassNames" 
        :key="className"
        @click="selectClass(className)"
      >
        <div class="class-item-content">
          {{ className }} ({{ getStudentCount(className) }})
          <el-popconfirm
            v-if="className !== '所有学生'"
            title="确定删除此班级吗？"
            @confirm="deleteClass(className)"
          >
            <template #reference>
              <el-icon class="delete-icon" @click.stop>
                <Delete />
              </el-icon>
            </template>
          </el-popconfirm>
        </div>
      </div>
    </div>

    <div v-if="isNewClassMode">
      <div class="class-form">
        <input 
          type="text" 
          placeholder="请输入班级名称" 
          class="class-input" 
          :value="newClassName"
          @input="$emit('update:new-class-name', $event.target.value)"
        >
        <div class="button-group">
          <el-button type="primary" @click="saveClass">保存</el-button>
          <el-button @click="cancelClass">取消</el-button>
        </div>
      </div>
    </div>
    <div v-else>
      <el-button  class="new-class-button" @click="toggleNewClassMode">+ 新建班级</el-button>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import { ElInput, ElButton, ElPopconfirm, ElMessage } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import { useStudentStore } from '@/stores/teacher/studentStore';

export default {
  components: {
    ElInput,
    ElButton,
    ElPopconfirm,
    Delete
  },
  props: {
    classSearchKeyword: {
      type: String,
      required: true
    },
    classNames: {
        type: Array,
        required: true,
        default: () => ['所有学生']
    },
    isNewClassMode: {
      type: Boolean,
      required: true
    },
    newClassName: {
      type: String,
      required: true
    },
    students: {
      type: Array,
      required: true
    },
    activeClass: {
      type: String,
      default: ''
    }
  },
  emits: [
    'update:class-search-keyword', 
    'toggle-new-class-mode', 
    'save-class', 
    'cancel-class', 
    'update:new-class-name', 
    'class-selected',
    'delete-class'
  ],
  setup(props, { emit }) {
    const studentStore = useStudentStore();

    const filteredClassNames = computed(() => {
      if (!props.classSearchKeyword) return props.classNames;
      return props.classNames.filter(className => 
        className.includes(props.classSearchKeyword)
      );
    });

    // 获取指定班级的学生数量
    const getStudentCount = (className) => {
      return studentStore.getStudentCount(className);
    };

    const selectClass = (className) => {
      emit('class-selected', className);
    };

    const toggleNewClassMode = () => {
      emit('toggle-new-class-mode'); // 触发父组件事件切换模式
    };

    const saveClass = () => {
      emit('save-class');
    };

    const deleteClass = (className) => {
      emit('delete-class', className);
    };

    const cancelClass = () => {
      emit('cancel-class');
    };

    return {
      filteredClassNames,
      getStudentCount,
      selectClass,
      toggleNewClassMode,
      saveClass,
      cancelClass,
      deleteClass
    };
  }
};
</script>

<style scoped>
.class-management {
  width: 100%;
}

.class-search-input {
  width: 100%;
  margin-bottom: 15px;
}

.class-form {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 15px;
}

.class-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.class-list {
  margin-top: 15px;
}

.class-item {
  padding: 8px;
  margin-bottom: 5px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.class-item:hover {
  background-color: #f5f7fa;
}

.class-item.selected {
  color: #409EFF;
  font-weight: bold;
}

.new-class-button {
  border: 1px solid #409EFF; 
  color: #409EFF; 
}

.student-count {
  color: #409EFF;
  font-size: 12px;
}

.class-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.delete-icon {
  color: #f56c6c;
  cursor: pointer;
  margin-left: 8px;
}

.delete-icon:hover {
  color: #ff0000;
}
</style>