{"name": "home", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@antv/g6": "^4.8.25", "@heroicons/vue": "^2.2.0", "@vueup/vue-quill": "^1.2.0", "ant-design-vue": "^4.0.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dompurify": "^3.2.6", "element-plus": "^2.10.2", "mammoth": "^1.9.1", "nanoid": "^5.1.5", "openai": "^5.8.2", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "uuid": "^11.1.0", "vditor": "^3.11.1", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "websocket": "^1.0.35"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "mrmime": "^2.0.1", "sass": "^1.89.1", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.7.7"}}