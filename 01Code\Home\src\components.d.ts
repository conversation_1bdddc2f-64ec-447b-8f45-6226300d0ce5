/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AnswerItem: typeof import('./components/teacher/AnswerItem.vue')['default']
    AnswerList: typeof import('./components/teacher/AnswerList.vue')['default']
    ConfirmDialog: typeof import('./components/ConfirmDialog.vue')['default']
    CustomNotification: typeof import('./components/CustomNotification.vue')['default']
    ElAnchor: typeof import('element-plus/es')['ElAnchor']
    ElAnchorLink: typeof import('element-plus/es')['ElAnchorLink']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRow: typeof import('element-plus/es')['ElRow']
    FilePreviewDialog: typeof import('./components/FilePreviewDialog.vue')['default']
    NavBar: typeof import('./components/NavBar.vue')['default']
    NotificationCenter: typeof import('./components/NotificationCenter.vue')['default']
    PermissionGuard: typeof import('./components/PermissionGuard.vue')['default']
    ResourcePreview: typeof import('./components/ResourcePreview.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SidebarLayout: typeof import('./components/Layout/SidebarLayout.vue')['default']
    TreeNode: typeof import('./components/resource/TreeNode.vue')['default']
  }
}
